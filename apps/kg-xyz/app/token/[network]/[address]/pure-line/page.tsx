/* eslint-disable @next/next/no-img-element */
import { TradingViewChart } from '@/components/TradingViewChart';
import { getTokenInfo } from '@/lib/okx';

export default async function TokenPage({
  params,
}: {
  params: Promise<{ network: string; address: string; debugWallet?: string }>;
}) {
  const { address, network } = await params;
  const chainId = network === 'sol' ? '501' : network === 'eth' ? '1' : network;
  const token = await getTokenInfo(address, chainId);

  if (!token) {
    throw new Error('Token not found');
  }

  return (
    <div className="h-[400px] bg-zinc-950">
      <TradingViewChart
        exchange={token.chainName}
        tokenAddress={token.tokenContractAddress}
        chainId={token.chainName === 'Solana' ? '501' : '1'} // Map chain name to OKX chainId
        inApp
      />
    </div>
  );
}
