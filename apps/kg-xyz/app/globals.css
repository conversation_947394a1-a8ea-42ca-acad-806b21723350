@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --radius: 0.5rem;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --rk-box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.12);
  }

}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Add CSS rules for handling nested dialogs */
/* When multiple dialogs are open, ensure secondary dialogs have higher z-index */
[role="dialog"][data-state="open"] {
  z-index: 50;
}

[role="dialog"][data-state="open"]~[role="dialog"][data-state="open"] {
  z-index: 60;
}

/* KryptogoKit wallet dialog should always appear on top and be interactive */
[data-rk] {
  z-index: 100 !important;
}

[data-rk] * {
  pointer-events: auto !important;
}

/* Hide scrollbars but maintain scrolling functionality */
@layer utilities {

  /* Hide scrollbars for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbars for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
}

/* Apply no-scrollbar to all scrollable elements by default */
* {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

*::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

/* Add scrollbar-hide utility class */
.scrollbar-hide {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

/* Feature popup progress animation */
@keyframes progress {
  from {
    stroke-dashoffset: 38;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* Feature popup pagination dot active path animation */
.pagination-dot {
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: none;
  outline: none;
}

.pagination-dot-inner {
  position: absolute;
  inset: 0;
  margin: auto;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.pagination-dot-active .pagination-dot-inner {
  width: 6px;
  height: 6px;
}

.pagination-dot-path {
  display: none;
}

.pagination-dot-active .pagination-dot-path {
  display: block;
  stroke-dasharray: 38;
  stroke-dashoffset: 38;
  animation: progress linear;
  /* Make animation exactly match the autoplay delay to ensure it completes right at transition */
  animation-duration: var(--autoplay-duration, 20000ms);
  animation-fill-mode: forwards;
  transform: rotate(-90deg);
  transform-origin: center;
}

/* Command menu styles */
[cmdk-dialog] {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
}

[cmdk-overlay] {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: -1;
}

[cmdk-root] {
  max-width: 640px;
  width: 100%;
  background: rgb(24, 24, 27);
  border-radius: 12px;
  overflow: hidden;
  padding: 0;
  font-family: var(--font-sans);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

[cmdk-input] {
  border: none;
  width: 100%;
  font-size: 14px;
  padding: 16px;
  outline: none;
  background: transparent;
  color: white;
  border-bottom: 1px solid rgb(39, 39, 42);
}

[cmdk-input]::placeholder {
  color: rgb(161, 161, 170);
}

[cmdk-list] {
  max-height: min(300px, calc(var(--cmdk-list-height)));
  min-height: 300px;
  overflow-y: auto;
  overscroll-behavior: contain;
  transition: 100ms ease;
  padding: 8px;
}

[cmdk-item] {
  content-visibility: auto;
  cursor: pointer;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: white;
  user-select: none;
  will-change: background, color;
  transition: all 150ms ease;
  transition-property: none;
}

[cmdk-item][data-selected='true'] {
  background: rgb(39, 39, 42);
  color: white;
}

[cmdk-item][data-disabled='true'] {
  color: rgb(161, 161, 170);
  cursor: not-allowed;
}

[cmdk-group-heading] {
  user-select: none;
  font-size: 12px;
  color: rgb(161, 161, 170);
  padding: 8px 12px;
  display: flex;
  align-items: center;
}

[cmdk-empty] {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  white-space: pre-wrap;
  color: rgb(161, 161, 170);
}

[cmdk-separator] {
  height: 1px;
  width: 100%;
  background: rgb(39, 39, 42);
  margin: 4px 0;
}
